package com.app.messej.data.repository.pagingSources

import android.util.Log
import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.app.messej.data.api.ChatAPIService
import com.app.messej.data.model.api.huddles.PrivateMessagesSuggestionResponse
import com.app.messej.data.model.enums.PrivateMessageUserType
import com.app.messej.data.model.enums.SearchType
import com.app.messej.data.utils.APIUtil
import com.app.messej.data.utils.ResultOf
import com.app.messej.data.utils.ResultOf.Companion.asException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class PrivateMessageSearchDataSource(private val api: ChatAPIService, private val keyword: String): PagingSource<PrivateMessageSearchDataSource.SearchKey, PrivateMessagesSuggestionResponse.User>() {

    data class SearchKey(
        val userType: PrivateMessageUserType,
        val searchType: SearchType,
        val page: Int
    )

    companion object {
        val initialKey: SearchKey
            get() = SearchKey(PrivateMessageUserType.INITIATED_CHATS, SearchType.EXACT_MATCH, 1)
    }

    override suspend fun load(params: LoadParams<SearchKey>): LoadResult<SearchKey, PrivateMessagesSuggestionResponse.User> = withContext(Dispatchers.IO) {
        try {

            val currentKey = params.key ?: initialKey
            val response = api.newPrivateMessageSearch(
                searchType = currentKey.searchType, userType = currentKey.userType, page = currentKey.page, keyword = keyword
            )

            when (val result = APIUtil.handleResponse(response)) {
                is ResultOf.APIError -> return@withContext LoadResult.Error(result.asException())
                is ResultOf.Error -> return@withContext LoadResult.Error(result.exception)
                is ResultOf.Success -> {
                    val list = result.value.users
                    Log.i("PrivateMessageSearchDataSource", "load: $list")
                    val keys = result.value
                    val nextKey = if (keys.nextPage) {
                        currentKey.copy(
                            page = currentKey.page + 1
                        )
                    } else if (keys.searchType != null) {
                        currentKey.copy(
                            searchType = keys.searchType, page = 1
                        )
                    } else if (currentKey.userType == PrivateMessageUserType.INITIATED_CHATS) {
                        SearchKey(PrivateMessageUserType.NOT_INITIATED_CHATS, SearchType.EXACT_MATCH, 1)
                    } else null
                    Log.i("PrivateMessageSearchDataSource", "nextKey: $nextKey")
                    LoadResult.Page(
                        data = list, prevKey = null, nextKey = nextKey
                    )
                }
            }

        } catch (e: Exception) {
            LoadResult.Error(e)
        }
    }

    override fun getRefreshKey(state: PagingState<SearchKey, PrivateMessagesSuggestionResponse.User>): SearchKey? = null
}